import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
/**
 * Terminal Chat Interface
 *
 * Main chat component providing real-time AI interaction
 * Handles model switching, approval workflows, and conversation management
 */
import { useState, useEffect, useCallback } from 'react';
import { Box, Text, useInput, useApp } from 'ink';
import chalk from 'chalk';
import { AgentLoop } from '../../utils/agent/agent-loop.js';
import { createInputItem } from '../../utils/input-utils.js';
import { executeSlashCommand, isSlashCommand } from '../../utils/slash-commands.js';
import { expandFileTags } from '../../utils/file-tag-utils.js';
import { addToHistory } from '../../utils/storage/command-history.js';
import { saveRollout } from '../../utils/storage/save-rollout.js';
import { logInfo, logError } from '../../utils/logger/log.js';
export function TerminalChat({ config, onExit }) {
    const { exit } = useApp();
    // State management
    const [model, setModel] = useState(config.model);
    const [provider, setProvider] = useState(config.provider);
    const [approvalPolicy, setApprovalPolicy] = useState(config.approvalMode);
    const [items, setItems] = useState([]);
    const [loading, setLoading] = useState(false);
    const [input, setInput] = useState('');
    const [sessionId] = useState(() => generateSessionId());
    // Agent loop instance
    const [agentLoop] = useState(() => new AgentLoop({
        model,
        provider,
        approvalPolicy,
        additionalWritableRoots: config.additionalWritableRoots || []
    }));
    // Update agent loop when config changes
    useEffect(() => {
        agentLoop.updateConfig({
            model,
            provider,
            approvalPolicy
        });
    }, [model, provider, approvalPolicy, agentLoop]);
    // Save session periodically
    useEffect(() => {
        if (items.length > 0) {
            saveRollout(sessionId, items, { model, provider, approvalMode: approvalPolicy });
        }
    }, [items, sessionId, model, provider, approvalPolicy]);
    // Handle keyboard input
    useInput((input, key) => {
        if (key.ctrl && input === 'c') {
            onExit();
            return;
        }
        if (key.return) {
            handleSubmit();
            return;
        }
        if (key.backspace || key.delete) {
            setInput(prev => prev.slice(0, -1));
            return;
        }
        if (input && !key.ctrl && !key.meta) {
            setInput(prev => prev + input);
        }
    });
    /**
     * Handle message submission
     */
    const handleSubmit = useCallback(async () => {
        if (!input.trim() || loading) {
            return;
        }
        const userInput = input.trim();
        setInput('');
        setLoading(true);
        try {
            logInfo('Processing user input', { input: userInput });
            // Add to command history
            addToHistory(userInput);
            // Check for slash commands
            if (isSlashCommand(userInput)) {
                const result = await executeSlashCommand(userInput);
                // Handle slash command results
                if (result.command === '/exit' || result.command === '/quit') {
                    onExit();
                    return;
                }
                // Add slash command result to conversation
                const commandResult = {
                    role: 'assistant',
                    content: result.result,
                    type: 'output',
                    timestamp: Date.now(),
                    metadata: {
                        model: 'system',
                        provider: 'system'
                    }
                };
                setItems(prev => [...prev, commandResult]);
                setLoading(false);
                return;
            }
            // Expand file tags
            const expandedInput = await expandFileTags(userInput, process.cwd());
            // Create input item
            const inputItem = await createInputItem(expandedInput, []);
            // Execute agent loop
            const results = await agentLoop.executeLoop(inputItem, {
                onDelta: (delta) => {
                    // Handle streaming text updates
                    console.log(chalk.gray(delta));
                },
                onComplete: (content) => {
                    logInfo('AI response completed', { length: content.length });
                },
                onError: (error) => {
                    logError('AI response error', new Error(error));
                },
                onToolCall: (toolCall) => {
                    logInfo('Tool call initiated', { name: toolCall.name });
                },
                onToolResult: (result) => {
                    logInfo('Tool execution completed', {
                        success: result.success,
                        command: result.metadata?.command
                    });
                },
                getCommandConfirmation: async (command, workdir) => {
                    return await handleCommandApproval(command, workdir);
                }
            });
            // Update conversation items
            setItems(prev => [...prev, ...results]);
            // Mark command as successful
            addToHistory(userInput, true);
        }
        catch (error) {
            logError('Error processing user input', error instanceof Error ? error : new Error(String(error)));
            // Add error message to conversation
            const errorItem = {
                role: 'assistant',
                content: `Error: ${error instanceof Error ? error.message : 'Unknown error occurred'}`,
                type: 'output',
                timestamp: Date.now(),
                metadata: {
                    model: 'system',
                    provider: 'system'
                }
            };
            setItems(prev => [...prev, errorItem]);
            // Mark command as failed
            addToHistory(userInput, false);
        }
        finally {
            setLoading(false);
        }
    }, [input, loading, agentLoop, onExit]);
    /**
     * Handle command approval
     */
    const handleCommandApproval = useCallback(async (command, workdir) => {
        // For now, implement simple approval based on policy
        if (approvalPolicy === 'full-auto') {
            return true;
        }
        if (approvalPolicy === 'auto-edit') {
            // Auto-approve safe commands
            const safeCommands = config.safeCommands || [];
            const commandName = command[0]?.toLowerCase();
            return safeCommands.includes(commandName);
        }
        // For 'suggest' mode, always require approval
        // TODO: Implement interactive approval UI
        console.log(chalk.yellow(`\nCommand approval required:`));
        console.log(chalk.gray(`Command: ${command.join(' ')}`));
        console.log(chalk.gray(`Working directory: ${workdir}`));
        console.log(chalk.blue('Auto-approving for demo purposes...'));
        return true; // Auto-approve for now
    }, [approvalPolicy, config.safeCommands]);
    /**
     * Render conversation items
     */
    const renderItems = () => {
        return items.map((item, index) => {
            if (item.type === 'message') {
                return (_jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [_jsx(Text, { color: "blue", bold: true, children: item.role === 'user' ? '👤 You:' : '🤖 Assistant:' }), item.content.map((content, contentIndex) => (_jsx(Text, { children: content.type === 'input_text' ? content.text : '[Image]' }, contentIndex)))] }, index));
            }
            if (item.type === 'output') {
                return (_jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [_jsx(Text, { color: "green", bold: true, children: "\uD83E\uDD16 Assistant:" }), _jsx(Text, { children: item.content })] }, index));
            }
            if (item.type === 'function_call') {
                return (_jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [_jsx(Text, { color: "yellow", bold: true, children: "\uD83D\uDD27 Tool Call:" }), _jsxs(Text, { color: "gray", children: [item.name, "(", item.arguments, ")"] })] }, index));
            }
            if (item.type === 'tool_result') {
                return (_jsxs(Box, { flexDirection: "column", marginBottom: 1, children: [_jsxs(Text, { color: item.success ? "green" : "red", bold: true, children: [item.success ? '✅' : '❌', " Tool Result:"] }), _jsx(Text, { children: item.result })] }, index));
            }
            return null;
        });
    };
    return (_jsxs(Box, { flexDirection: "column", height: "100%", children: [_jsx(Box, { borderStyle: "single", paddingX: 1, children: _jsxs(Text, { color: "blue", bold: true, children: ["Kritrima AI CLI - ", provider, "/", model, " (", approvalPolicy, ")"] }) }), _jsxs(Box, { flexDirection: "column", flexGrow: 1, paddingX: 1, paddingY: 1, children: [renderItems(), loading && (_jsx(Box, { children: _jsx(Text, { color: "yellow", children: "\uD83E\uDD14 Thinking..." }) }))] }), _jsxs(Box, { borderStyle: "single", paddingX: 1, children: [_jsx(Text, { color: "cyan", children: "\uD83D\uDCAC " }), _jsx(Text, { children: input }), _jsx(Text, { color: "gray", children: "\u2588" })] }), _jsx(Box, { paddingX: 1, children: _jsxs(Text, { color: "gray", dimColor: true, children: ["Press Ctrl+C to exit \u2022 Type /help for commands \u2022 ", items.length, " messages"] }) })] }));
}
/**
 * Generate unique session ID
 */
function generateSessionId() {
    return Math.random().toString(36).substring(2, 15) +
        Math.random().toString(36).substring(2, 15);
}
//# sourceMappingURL=terminal-chat.js.map