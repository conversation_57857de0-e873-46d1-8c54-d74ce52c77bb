{"version": 3, "file": "terminal-chat.js", "sourceRoot": "", "sources": ["../../../src/components/chat/terminal-chat.tsx"], "names": [], "mappings": ";AAAA;;;;;GAKG;AAEH,OAAc,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,OAAO,CAAC;AAChE,OAAO,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,KAAK,CAAC;AAClD,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,SAAS,EAAE,MAAM,iCAAiC,CAAC;AAC5D,OAAO,EAAE,eAAe,EAAE,MAAM,4BAA4B,CAAC;AAC7D,OAAO,EAAE,mBAAmB,EAAE,cAAc,EAAE,MAAM,+BAA+B,CAAC;AACpF,OAAO,EAAE,cAAc,EAAE,MAAM,+BAA+B,CAAC;AAC/D,OAAO,EAAE,YAAY,EAAE,MAAM,wCAAwC,CAAC;AACtE,OAAO,EAAE,WAAW,EAAE,MAAM,qCAAqC,CAAC;AAClE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,2BAA2B,CAAC;AAQ9D,MAAM,UAAU,YAAY,CAAC,EAAE,MAAM,EAAE,MAAM,EAAqB;IAChE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,EAAE,CAAC;IAE1B,mBAAmB;IACnB,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAS,MAAM,CAAC,KAAK,CAAC,CAAC;IACzD,MAAM,CAAC,QAAQ,EAAE,WAAW,CAAC,GAAG,QAAQ,CAAS,MAAM,CAAC,QAAQ,CAAC,CAAC;IAClE,MAAM,CAAC,cAAc,EAAE,iBAAiB,CAAC,GAAG,QAAQ,CAAiB,MAAM,CAAC,YAAY,CAAC,CAAC;IAC1F,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAiB,EAAE,CAAC,CAAC;IACvD,MAAM,CAAC,OAAO,EAAE,UAAU,CAAC,GAAG,QAAQ,CAAU,KAAK,CAAC,CAAC;IACvD,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAS,EAAE,CAAC,CAAC;IAC/C,MAAM,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAS,GAAG,EAAE,CAAC,iBAAiB,EAAE,CAAC,CAAC;IAEhE,sBAAsB;IACtB,MAAM,CAAC,SAAS,CAAC,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC,IAAI,SAAS,CAAC;QAC/C,KAAK;QACL,QAAQ;QACR,cAAc;QACd,uBAAuB,EAAE,MAAM,CAAC,uBAAuB,IAAI,EAAE;KAC9D,CAAC,CAAC,CAAC;IAEJ,wCAAwC;IACxC,SAAS,CAAC,GAAG,EAAE;QACb,SAAS,CAAC,YAAY,CAAC;YACrB,KAAK;YACL,QAAQ;YACR,cAAc;SACf,CAAC,CAAC;IACL,CAAC,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,cAAc,EAAE,SAAS,CAAC,CAAC,CAAC;IAEjD,4BAA4B;IAC5B,SAAS,CAAC,GAAG,EAAE;QACb,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,WAAW,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,YAAY,EAAE,cAAc,EAAE,CAAC,CAAC;QACnF,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,cAAc,CAAC,CAAC,CAAC;IAExD,wBAAwB;IACxB,QAAQ,CAAC,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;QACtB,IAAI,GAAG,CAAC,IAAI,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC;YAC9B,MAAM,EAAE,CAAC;YACT,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YACf,YAAY,EAAE,CAAC;YACf,OAAO;QACT,CAAC;QAED,IAAI,GAAG,CAAC,SAAS,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC;YAChC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;YACpC,OAAO;QACT,CAAC;QAED,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACpC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC;QACjC,CAAC;IACH,CAAC,CAAC,CAAC;IAEH;;OAEG;IACH,MAAM,YAAY,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;QAC1C,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,OAAO,EAAE,CAAC;YAC7B,OAAO;QACT,CAAC;QAED,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;QAC/B,QAAQ,CAAC,EAAE,CAAC,CAAC;QACb,UAAU,CAAC,IAAI,CAAC,CAAC;QAEjB,IAAI,CAAC;YACH,OAAO,CAAC,uBAAuB,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;YAEvD,yBAAyB;YACzB,YAAY,CAAC,SAAS,CAAC,CAAC;YAExB,2BAA2B;YAC3B,IAAI,cAAc,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,MAAM,MAAM,GAAG,MAAM,mBAAmB,CAAC,SAAS,CAAC,CAAC;gBAEpD,+BAA+B;gBAC/B,IAAI,MAAM,CAAC,OAAO,KAAK,OAAO,IAAI,MAAM,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;oBAC7D,MAAM,EAAE,CAAC;oBACT,OAAO;gBACT,CAAC;gBAED,2CAA2C;gBAC3C,MAAM,aAAa,GAAiB;oBAClC,IAAI,EAAE,WAAW;oBACjB,OAAO,EAAE,MAAM,CAAC,MAAM;oBACtB,IAAI,EAAE,QAAQ;oBACd,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;oBACrB,QAAQ,EAAE;wBACR,KAAK,EAAE,QAAQ;wBACf,QAAQ,EAAE,QAAQ;qBACnB;iBACF,CAAC;gBAEF,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,aAAa,CAAC,CAAC,CAAC;gBAC3C,UAAU,CAAC,KAAK,CAAC,CAAC;gBAClB,OAAO;YACT,CAAC;YAED,mBAAmB;YACnB,MAAM,aAAa,GAAG,MAAM,cAAc,CAAC,SAAS,EAAE,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;YAErE,oBAAoB;YACpB,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,aAAa,EAAE,EAAE,CAAC,CAAC;YAE3D,qBAAqB;YACrB,MAAM,OAAO,GAAG,MAAM,SAAS,CAAC,WAAW,CACzC,SAAS,EACT;gBACE,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;oBACjB,gCAAgC;oBAChC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;gBACjC,CAAC;gBACD,UAAU,EAAE,CAAC,OAAO,EAAE,EAAE;oBACtB,OAAO,CAAC,uBAAuB,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC;gBAC/D,CAAC;gBACD,OAAO,EAAE,CAAC,KAAK,EAAE,EAAE;oBACjB,QAAQ,CAAC,mBAAmB,EAAE,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;gBAClD,CAAC;gBACD,UAAU,EAAE,CAAC,QAAQ,EAAE,EAAE;oBACvB,OAAO,CAAC,qBAAqB,EAAE,EAAE,IAAI,EAAE,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC;gBAC1D,CAAC;gBACD,YAAY,EAAE,CAAC,MAAM,EAAE,EAAE;oBACvB,OAAO,CAAC,0BAA0B,EAAE;wBAClC,OAAO,EAAE,MAAM,CAAC,OAAO;wBACvB,OAAO,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO;qBAClC,CAAC,CAAC;gBACL,CAAC;gBACD,sBAAsB,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,EAAE;oBACjD,OAAO,MAAM,qBAAqB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;gBACvD,CAAC;aACF,CACF,CAAC;YAEF,4BAA4B;YAC5B,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,GAAG,OAAO,CAAC,CAAC,CAAC;YAExC,6BAA6B;YAC7B,YAAY,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAEhC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,QAAQ,CAAC,6BAA6B,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAEnG,oCAAoC;YACpC,MAAM,SAAS,GAAiB;gBAC9B,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,UAAU,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,EAAE;gBACtF,IAAI,EAAE,QAAQ;gBACd,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE;gBACrB,QAAQ,EAAE;oBACR,KAAK,EAAE,QAAQ;oBACf,QAAQ,EAAE,QAAQ;iBACnB;aACF,CAAC;YAEF,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,GAAG,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;YAEvC,yBAAyB;YACzB,YAAY,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;QACjC,CAAC;gBAAS,CAAC;YACT,UAAU,CAAC,KAAK,CAAC,CAAC;QACpB,CAAC;IACH,CAAC,EAAE,CAAC,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC,CAAC;IAExC;;OAEG;IACH,MAAM,qBAAqB,GAAG,WAAW,CAAC,KAAK,EAC7C,OAAiB,EACjB,OAAe,EACG,EAAE;QACpB,qDAAqD;QACrD,IAAI,cAAc,KAAK,WAAW,EAAE,CAAC;YACnC,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,cAAc,KAAK,WAAW,EAAE,CAAC;YACnC,6BAA6B;YAC7B,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,IAAI,EAAE,CAAC;YAC/C,MAAM,WAAW,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,CAAC;YAC9C,OAAO,YAAY,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAC5C,CAAC;QAED,8CAA8C;QAC9C,0CAA0C;QAC1C,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,8BAA8B,CAAC,CAAC,CAAC;QAC1D,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,sBAAsB,OAAO,EAAE,CAAC,CAAC,CAAC;QACzD,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,qCAAqC,CAAC,CAAC,CAAC;QAE/D,OAAO,IAAI,CAAC,CAAC,uBAAuB;IACtC,CAAC,EAAE,CAAC,cAAc,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;IAE1C;;OAEG;IACH,MAAM,WAAW,GAAG,GAAG,EAAE;QACvB,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;YAC/B,IAAI,IAAI,CAAC,IAAI,KAAK,SAAS,EAAE,CAAC;gBAC5B,OAAO,CACL,MAAC,GAAG,IAAa,aAAa,EAAC,QAAQ,EAAC,YAAY,EAAE,CAAC,aACrD,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,kBACpB,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,eAAe,GAC9C,EACN,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,YAAY,EAAE,EAAE,CAAC,CAC3C,KAAC,IAAI,cACF,OAAO,CAAC,IAAI,KAAK,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS,IADhD,YAAY,CAEhB,CACR,CAAC,KARM,KAAK,CAST,CACP,CAAC;YACJ,CAAC;YAED,IAAI,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;gBAC3B,OAAO,CACL,MAAC,GAAG,IAAa,aAAa,EAAC,QAAQ,EAAC,YAAY,EAAE,CAAC,aACrD,KAAC,IAAI,IAAC,KAAK,EAAC,OAAO,EAAC,IAAI,8CAAqB,EAC7C,KAAC,IAAI,cAAE,IAAI,CAAC,OAAO,GAAQ,KAFnB,KAAK,CAGT,CACP,CAAC;YACJ,CAAC;YAED,IAAI,IAAI,CAAC,IAAI,KAAK,eAAe,EAAE,CAAC;gBAClC,OAAO,CACL,MAAC,GAAG,IAAa,aAAa,EAAC,QAAQ,EAAC,YAAY,EAAE,CAAC,aACrD,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,EAAC,IAAI,8CAAqB,EAC9C,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,aAAE,IAAI,CAAC,IAAI,OAAG,IAAI,CAAC,SAAS,SAAS,KAF/C,KAAK,CAGT,CACP,CAAC;YACJ,CAAC;YAED,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,EAAE,CAAC;gBAChC,OAAO,CACL,MAAC,GAAG,IAAa,aAAa,EAAC,QAAQ,EAAC,YAAY,EAAE,CAAC,aACrD,MAAC,IAAI,IAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,mBAC9C,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,qBACpB,EACP,KAAC,IAAI,cAAE,IAAI,CAAC,MAAM,GAAQ,KAJlB,KAAK,CAKT,CACP,CAAC;YACJ,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEF,OAAO,CACL,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,MAAM,EAAC,MAAM,aAEvC,KAAC,GAAG,IAAC,WAAW,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,YACnC,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,IAAI,yCACF,QAAQ,OAAG,KAAK,QAAI,cAAc,SAChD,GACH,EAGN,MAAC,GAAG,IAAC,aAAa,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,aAC9D,WAAW,EAAE,EAEb,OAAO,IAAI,CACV,KAAC,GAAG,cACF,KAAC,IAAI,IAAC,KAAK,EAAC,QAAQ,yCAAsB,GACtC,CACP,IACG,EAGN,MAAC,GAAG,IAAC,WAAW,EAAC,QAAQ,EAAC,QAAQ,EAAE,CAAC,aACnC,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,8BAAW,EAC7B,KAAC,IAAI,cAAE,KAAK,GAAQ,EACpB,KAAC,IAAI,IAAC,KAAK,EAAC,MAAM,uBAAS,IACvB,EAGN,KAAC,GAAG,IAAC,QAAQ,EAAE,CAAC,YACd,MAAC,IAAI,IAAC,KAAK,EAAC,MAAM,EAAC,QAAQ,kFACyB,KAAK,CAAC,MAAM,iBACzD,GACH,IACF,CACP,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,iBAAiB;IACxB,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;QAC3C,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;AACrD,CAAC"}