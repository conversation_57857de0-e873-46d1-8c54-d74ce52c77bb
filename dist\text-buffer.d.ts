/**
 * Sophisticated Text Buffer System
 *
 * Provides advanced multi-line text editing capabilities with:
 * - Undo/Redo system
 * - Viewport management
 * - Unicode support
 * - Cursor positioning
 * - Text manipulation operations
 */
export interface UndoState {
    lines: string[];
    cursorRow: number;
    cursorCol: number;
    version: number;
}
export interface ViewportInfo {
    scrollRow: number;
    scrollCol: number;
    visibleRows: number;
    visibleCols: number;
}
export interface CursorPosition {
    row: number;
    col: number;
}
export default class TextBuffer {
    private lines;
    private cursorRow;
    private cursorCol;
    private scrollRow;
    private scrollCol;
    private version;
    private undoStack;
    private redoStack;
    private maxUndoSteps;
    constructor(initialText?: string);
    /**
     * Get all text as a single string
     */
    getText(): string;
    /**
     * Set all text, replacing current content
     */
    setText(text: string): void;
    /**
     * Insert text at current cursor position
     */
    insertText(text: string): void;
    /**
     * Delete character at cursor position
     */
    deleteChar(): void;
    /**
     * Delete character after cursor position
     */
    deleteCharForward(): void;
    /**
     * Move cursor to specific position
     */
    setCursor(row: number, col: number): void;
    /**
     * Get current cursor position
     */
    getCursor(): CursorPosition;
    /**
     * Move cursor left
     */
    moveCursorLeft(): void;
    /**
     * Move cursor right
     */
    moveCursorRight(): void;
    /**
     * Move cursor up
     */
    moveCursorUp(): void;
    /**
     * Move cursor down
     */
    moveCursorDown(): void;
    /**
     * Move cursor to beginning of line
     */
    moveCursorToLineStart(): void;
    /**
     * Move cursor to end of line
     */
    moveCursorToLineEnd(): void;
    /**
     * Move cursor to beginning of buffer
     */
    moveCursorToStart(): void;
    /**
     * Move cursor to end of buffer
     */
    moveCursorToEnd(): void;
    /**
     * Move cursor to next word
     */
    moveCursorToNextWord(): void;
    /**
     * Move cursor to previous word
     */
    moveCursorToPrevWord(): void;
    /**
     * Delete word before cursor
     */
    deleteWordBackward(): void;
    /**
     * Insert new line at cursor
     */
    insertNewLine(): void;
    /**
     * Delete current line
     */
    deleteLine(): void;
    /**
     * Delete from cursor to end of line
     */
    deleteToLineEnd(): void;
    private saveUndoState;
    /**
     * Undo last operation
     */
    undo(): boolean;
    /**
     * Redo last undone operation
     */
    redo(): boolean;
    /**
     * Get viewport information
     */
    getViewport(): ViewportInfo;
    /**
     * Ensure cursor is visible in viewport
     */
    ensureCursorVisible(viewportRows: number, viewportCols: number): void;
    /**
     * Get line count
     */
    getLineCount(): number;
    /**
     * Get line at index
     */
    getLine(index: number): string;
    /**
     * Get all lines
     */
    getLines(): string[];
    /**
     * Get buffer version (for change detection)
     */
    getVersion(): number;
    /**
     * Check if buffer is empty
     */
    isEmpty(): boolean;
    /**
     * Get character count
     */
    getCharCount(): number;
}
//# sourceMappingURL=text-buffer.d.ts.map