/**
 * Autonomous Agent Loop & Tool Calling System
 *
 * The heart of the AI system implementing sophisticated autonomous workflow
 * <PERSON>les tool calling, conversation management, and state persistence
 */
import type { ApprovalPolicy, ResponseItem, ResponseInputItem, ResponseFunctionToolCall, ResponseToolResult } from '../../types/index.js';
export interface AgentLoopConfig {
    model: string;
    provider: string;
    approvalPolicy: ApprovalPolicy;
    maxIterations?: number;
    timeout?: number;
    additionalWritableRoots?: string[];
}
export interface AgentLoopCallbacks {
    onDelta?: (delta: string) => void;
    onComplete?: (content: string) => void;
    onError?: (error: string) => void;
    onToolCall?: (toolCall: ResponseFunctionToolCall) => void;
    onToolResult?: (result: ResponseToolResult) => void;
    getCommandConfirmation?: (command: string[], workdir: string) => Promise<boolean>;
}
/**
 * Core Agent Loop Class
 */
export declare class AgentLoop {
    private model;
    private provider;
    private oai;
    private approvalPolicy;
    private transcript;
    private pendingAborts;
    private cumulativeThinkingMs;
    private additionalWritableRoots;
    private config;
    constructor(config: AgentLoopConfig);
    /**
     * Execute agent loop with user input
     */
    executeLoop(userInput: ResponseInputItem, callbacks?: AgentLoopCallbacks, maxIterations?: number): Promise<ResponseItem[]>;
    /**
     * Handle function call execution
     */
    private handleFunctionCall;
    /**
     * Handle shell command execution
     */
    private handleShellCommand;
    /**
     * Check if command is safe for auto-approval
     */
    private isCommandSafe;
    /**
     * Get available tools based on approval policy
     */
    private getAvailableTools;
    /**
     * Get conversation transcript
     */
    getTranscript(): ResponseInputItem[];
    /**
     * Clear conversation transcript
     */
    clearTranscript(): void;
    /**
     * Get cumulative thinking time
     */
    getCumulativeThinkingTime(): number;
    /**
     * Update configuration
     */
    updateConfig(newConfig: Partial<AgentLoopConfig>): void;
}
//# sourceMappingURL=agent-loop.d.ts.map