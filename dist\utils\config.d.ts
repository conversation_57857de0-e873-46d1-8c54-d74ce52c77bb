/**
 * Configuration & State Management System
 *
 * Handles multi-format configuration support with hierarchy:
 * 1. Command Line Arguments (highest priority)
 * 2. Environment Variables
 * 3. Project Configuration (.kritrima-ai/config.json)
 * 4. User Configuration (~/.kritrima-ai/config.json)
 * 5. Default Values (lowest priority)
 */
import type { AppConfig } from '../types/index.js';
/**
 * Load configuration from all sources with proper hierarchy
 */
export declare function loadConfig(workingDir?: string): AppConfig;
/**
 * Discover project documentation path
 */
export declare function discoverProjectDocPath(startDir: string): string | null;
/**
 * Get API key for provider
 */
export declare function getApiKey(provider?: string): string | undefined;
/**
 * Get base URL for provider with environment variable override support
 */
export declare function getBaseUrl(provider?: string): string;
/**
 * Save user configuration
 */
export declare function saveUserConfig(config: Partial<AppConfig>): void;
/**
 * Clear cached configuration (for testing)
 */
export declare function clearConfigCache(): void;
//# sourceMappingURL=config.d.ts.map