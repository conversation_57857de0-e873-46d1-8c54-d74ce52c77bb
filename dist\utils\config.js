/**
 * Configuration & State Management System
 *
 * Handles multi-format configuration support with hierarchy:
 * 1. Command Line Arguments (highest priority)
 * 2. Environment Variables
 * 3. Project Configuration (.kritrima-ai/config.json)
 * 4. User Configuration (~/.kritrima-ai/config.json)
 * 5. Default Values (lowest priority)
 */
import { readFileSync, existsSync, mkdirSync, writeFileSync } from 'fs';
import { join, dirname } from 'path';
import { homedir } from 'os';
import { parse as parseYaml } from 'yaml';
import { providers, getProviderConfig } from './providers.js';
// Default configuration
const DEFAULT_CONFIG = {
    model: 'gpt-4',
    provider: 'openai',
    approvalMode: 'suggest',
    maxTokens: 4096,
    temperature: 0.7,
    timeout: 30000,
    enableNotifications: true,
    enableLogging: false,
    safeCommands: ['ls', 'cat', 'grep', 'find', 'head', 'tail', 'wc', 'echo', 'pwd', 'which'],
    dangerousCommands: ['rm', 'sudo', 'chmod', 'chown', 'dd', 'mkfs', 'fdisk'],
    additionalWritableRoots: []
};
let cachedConfig = null;
/**
 * Load configuration from all sources with proper hierarchy
 */
export function loadConfig(workingDir = process.cwd()) {
    if (cachedConfig) {
        return cachedConfig;
    }
    let config = { ...DEFAULT_CONFIG };
    // 1. Load user configuration
    const userConfig = loadUserConfig();
    if (userConfig) {
        config = { ...config, ...userConfig };
    }
    // 2. Load project configuration
    const projectConfig = loadProjectConfig(workingDir);
    if (projectConfig) {
        config = { ...config, ...projectConfig };
    }
    // 3. Apply environment variables
    applyEnvironmentVariables(config);
    // 4. Discover project documentation
    const docPath = discoverProjectDocPath(workingDir);
    if (docPath) {
        config.projectDocPath = docPath;
    }
    // Validate and cache
    validateConfig(config);
    cachedConfig = config;
    return config;
}
/**
 * Load user configuration from ~/.kritrima-ai/config.json
 */
function loadUserConfig() {
    const userConfigDir = join(homedir(), '.kritrima-ai');
    const userConfigPath = join(userConfigDir, 'config.json');
    if (!existsSync(userConfigPath)) {
        return null;
    }
    try {
        const content = readFileSync(userConfigPath, 'utf-8');
        return JSON.parse(content);
    }
    catch (error) {
        console.warn(`Warning: Could not parse user config at ${userConfigPath}`);
        return null;
    }
}
/**
 * Load project configuration from .kritrima-ai/config.json or config.yaml
 */
function loadProjectConfig(workingDir) {
    const projectConfigDir = join(workingDir, '.kritrima-ai');
    // Try JSON first
    const jsonPath = join(projectConfigDir, 'config.json');
    if (existsSync(jsonPath)) {
        try {
            const content = readFileSync(jsonPath, 'utf-8');
            return JSON.parse(content);
        }
        catch (error) {
            console.warn(`Warning: Could not parse project config at ${jsonPath}`);
        }
    }
    // Try YAML
    const yamlPath = join(projectConfigDir, 'config.yaml');
    if (existsSync(yamlPath)) {
        try {
            const content = readFileSync(yamlPath, 'utf-8');
            return parseYaml(content);
        }
        catch (error) {
            console.warn(`Warning: Could not parse project config at ${yamlPath}`);
        }
    }
    return null;
}
/**
 * Apply environment variable overrides
 */
function applyEnvironmentVariables(config) {
    if (process.env.KRITRIMA_AI_MODEL) {
        config.model = process.env.KRITRIMA_AI_MODEL;
    }
    if (process.env.KRITRIMA_AI_PROVIDER) {
        config.provider = process.env.KRITRIMA_AI_PROVIDER;
    }
    if (process.env.KRITRIMA_AI_APPROVAL_MODE) {
        config.approvalMode = process.env.KRITRIMA_AI_APPROVAL_MODE;
    }
    if (process.env.KRITRIMA_AI_MAX_TOKENS) {
        config.maxTokens = parseInt(process.env.KRITRIMA_AI_MAX_TOKENS, 10);
    }
    if (process.env.KRITRIMA_AI_TEMPERATURE) {
        config.temperature = parseFloat(process.env.KRITRIMA_AI_TEMPERATURE);
    }
    if (process.env.KRITRIMA_AI_ENABLE_NOTIFICATIONS) {
        config.enableNotifications = process.env.KRITRIMA_AI_ENABLE_NOTIFICATIONS === 'true';
    }
    if (process.env.KRITRIMA_AI_ENABLE_LOGGING) {
        config.enableLogging = process.env.KRITRIMA_AI_ENABLE_LOGGING === 'true';
    }
}
/**
 * Discover project documentation path
 */
export function discoverProjectDocPath(startDir) {
    const candidates = ['AGENTS.md', 'README.md', 'docs/README.md', 'docs/AGENTS.md'];
    let currentDir = startDir;
    // Search upward through directory tree
    while (currentDir !== dirname(currentDir)) {
        for (const candidate of candidates) {
            const fullPath = join(currentDir, candidate);
            if (existsSync(fullPath)) {
                return fullPath;
            }
        }
        currentDir = dirname(currentDir);
    }
    return null;
}
/**
 * Get API key for provider
 */
export function getApiKey(provider = 'openai') {
    const config = loadConfig();
    const providerInfo = getProviderConfig(provider, config.providers);
    if (providerInfo) {
        return process.env[providerInfo.envKey];
    }
    // Fallback to custom environment variable
    return process.env[`${provider.toUpperCase()}_API_KEY`];
}
/**
 * Get base URL for provider with environment variable override support
 */
export function getBaseUrl(provider = 'openai') {
    const config = loadConfig();
    const providerInfo = getProviderConfig(provider, config.providers);
    // Check for environment variable override
    const envOverride = process.env[`${provider.toUpperCase()}_BASE_URL`];
    if (envOverride) {
        return envOverride;
    }
    return providerInfo?.baseURL || providers.openai.baseURL;
}
/**
 * Save user configuration
 */
export function saveUserConfig(config) {
    const userConfigDir = join(homedir(), '.kritrima-ai');
    const userConfigPath = join(userConfigDir, 'config.json');
    // Ensure directory exists
    if (!existsSync(userConfigDir)) {
        mkdirSync(userConfigDir, { recursive: true });
    }
    try {
        writeFileSync(userConfigPath, JSON.stringify(config, null, 2));
    }
    catch (error) {
        console.warn(`Warning: Could not save user config to ${userConfigPath}`);
    }
}
/**
 * Validate configuration
 */
function validateConfig(config) {
    if (!config.model || typeof config.model !== 'string') {
        throw new Error('Invalid model configuration');
    }
    if (!config.provider || typeof config.provider !== 'string') {
        throw new Error('Invalid provider configuration');
    }
    if (!['suggest', 'auto-edit', 'full-auto'].includes(config.approvalMode)) {
        throw new Error('Invalid approval mode configuration');
    }
    if (config.maxTokens && (config.maxTokens < 1 || config.maxTokens > 200000)) {
        throw new Error('Invalid maxTokens configuration');
    }
    if (config.temperature && (config.temperature < 0 || config.temperature > 2)) {
        throw new Error('Invalid temperature configuration');
    }
}
/**
 * Clear cached configuration (for testing)
 */
export function clearConfigCache() {
    cachedConfig = null;
}
//# sourceMappingURL=config.js.map