/**
 * Input Utilities for Multi-Modal AI Interaction
 *
 * Handles text and image input processing for AI conversations
 * Supports various image formats and automatic optimization
 */
import type { ResponseInputItem } from '../types/index.js';
/**
 * Create input item from text and image paths
 */
export declare function createInputItem(text: string, imagePaths?: string[]): Promise<ResponseInputItem>;
/**
 * Extract image paths from text (e.g., @image.png)
 */
export declare function extractImagePaths(text: string): {
    cleanText: string;
    imagePaths: string[];
};
/**
 * Validate image paths exist and are accessible
 */
export declare function validateImagePaths(imagePaths: string[]): {
    valid: string[];
    invalid: string[];
};
/**
 * Get image information without loading
 */
export declare function getImageInfo(imagePath: string): {
    exists: boolean;
    size?: number;
    format?: string;
    supported?: boolean;
};
/**
 * Format file size for display
 */
export declare function formatFileSize(bytes: number): string;
/**
 * Create system message for context
 */
export declare function createSystemMessage(content: string): ResponseInputItem;
/**
 * Create assistant message for conversation history
 */
export declare function createAssistantMessage(content: string): ResponseInputItem;
//# sourceMappingURL=input-utils.d.ts.map