/**
 * Input Utilities for Multi-Modal AI Interaction
 *
 * Handles text and image input processing for AI conversations
 * Supports various image formats and automatic optimization
 */
import { readFileSync, existsSync, statSync } from 'fs';
import { extname } from 'path';
// Supported image formats
const SUPPORTED_IMAGE_FORMATS = ['.png', '.jpg', '.jpeg', '.webp', '.gif'];
// Maximum image size (5MB)
const MAX_IMAGE_SIZE = 5 * 1024 * 1024;
/**
 * Create input item from text and image paths
 */
export async function createInputItem(text, imagePaths = []) {
    const content = [];
    // Add text content if provided
    if (text.trim()) {
        content.push({
            type: 'input_text',
            text: text.trim()
        });
    }
    // Process image paths
    for (const imagePath of imagePaths) {
        try {
            const imageData = await processImage(imagePath);
            content.push({
                type: 'input_image',
                image: imageData
            });
        }
        catch (error) {
            console.warn(`Warning: Could not process image ${imagePath}:`, error);
            // Add error message to text content
            content.push({
                type: 'input_text',
                text: `[Error: Could not load image ${imagePath}]`
            });
        }
    }
    return {
        role: 'user',
        content,
        type: 'message',
        timestamp: Date.now()
    };
}
/**
 * Process image file for AI consumption
 */
async function processImage(imagePath) {
    // Validate file exists
    if (!existsSync(imagePath)) {
        throw new Error(`Image file not found: ${imagePath}`);
    }
    // Validate file format
    const ext = extname(imagePath).toLowerCase();
    if (!SUPPORTED_IMAGE_FORMATS.includes(ext)) {
        throw new Error(`Unsupported image format: ${ext}. Supported formats: ${SUPPORTED_IMAGE_FORMATS.join(', ')}`);
    }
    // Check file size
    const stats = statSync(imagePath);
    if (stats.size > MAX_IMAGE_SIZE) {
        throw new Error(`Image file too large: ${stats.size} bytes. Maximum size: ${MAX_IMAGE_SIZE} bytes`);
    }
    // Read and encode image
    const imageBuffer = readFileSync(imagePath);
    const base64Data = imageBuffer.toString('base64');
    // Determine MIME type
    const mimeType = getMimeType(ext);
    const dataUrl = `data:${mimeType};base64,${base64Data}`;
    // Determine detail level based on file size
    let detail = 'auto';
    if (stats.size < 512 * 1024) { // < 512KB
        detail = 'low';
    }
    else if (stats.size > 2 * 1024 * 1024) { // > 2MB
        detail = 'high';
    }
    return {
        url: dataUrl,
        detail
    };
}
/**
 * Get MIME type for image extension
 */
function getMimeType(ext) {
    const mimeTypes = {
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.webp': 'image/webp',
        '.gif': 'image/gif'
    };
    return mimeTypes[ext.toLowerCase()] || 'image/jpeg';
}
/**
 * Extract image paths from text (e.g., @image.png)
 */
export function extractImagePaths(text) {
    const imagePattern = /@([^\s]+\.(png|jpg|jpeg|webp|gif))/gi;
    const imagePaths = [];
    const cleanText = text.replace(imagePattern, (match, path) => {
        imagePaths.push(path);
        return `[Image: ${path}]`;
    });
    return { cleanText, imagePaths };
}
/**
 * Validate image paths exist and are accessible
 */
export function validateImagePaths(imagePaths) {
    const valid = [];
    const invalid = [];
    for (const path of imagePaths) {
        try {
            if (existsSync(path)) {
                const stats = statSync(path);
                if (stats.isFile()) {
                    const ext = extname(path).toLowerCase();
                    if (SUPPORTED_IMAGE_FORMATS.includes(ext)) {
                        if (stats.size <= MAX_IMAGE_SIZE) {
                            valid.push(path);
                        }
                        else {
                            invalid.push(`${path} (too large: ${stats.size} bytes)`);
                        }
                    }
                    else {
                        invalid.push(`${path} (unsupported format: ${ext})`);
                    }
                }
                else {
                    invalid.push(`${path} (not a file)`);
                }
            }
            else {
                invalid.push(`${path} (not found)`);
            }
        }
        catch (error) {
            invalid.push(`${path} (access error: ${error})`);
        }
    }
    return { valid, invalid };
}
/**
 * Get image information without loading
 */
export function getImageInfo(imagePath) {
    try {
        if (!existsSync(imagePath)) {
            return { exists: false };
        }
        const stats = statSync(imagePath);
        const ext = extname(imagePath).toLowerCase();
        const supported = SUPPORTED_IMAGE_FORMATS.includes(ext);
        return {
            exists: true,
            size: stats.size,
            format: ext,
            supported
        };
    }
    catch (error) {
        return { exists: false };
    }
}
/**
 * Format file size for display
 */
export function formatFileSize(bytes) {
    if (bytes === 0)
        return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
/**
 * Create system message for context
 */
export function createSystemMessage(content) {
    return {
        role: 'system',
        content: [{
                type: 'input_text',
                text: content
            }],
        type: 'message',
        timestamp: Date.now()
    };
}
/**
 * Create assistant message for conversation history
 */
export function createAssistantMessage(content) {
    return {
        role: 'assistant',
        content: [{
                type: 'input_text',
                text: content
            }],
        type: 'message',
        timestamp: Date.now()
    };
}
//# sourceMappingURL=input-utils.js.map