{"version": 3, "file": "package-manager-detector.js", "sourceRoot": "", "sources": ["../../src/utils/package-manager-detector.ts"], "names": [], "mappings": "AAAA;;;;;GAKG;AAEH,OAAO,EAAE,QAAQ,EAAE,MAAM,eAAe,CAAC;AACzC,OAAO,EAAE,UAAU,EAAE,MAAM,IAAI,CAAC;AAChC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,MAAM,CAAC;AAIzC;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,qBAAqB;IACzC,IAAI,CAAC;QACH,yCAAyC;QACzC,MAAM,QAAQ,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACjC,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;QAClC,MAAM,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAEpC,wDAAwD;QACxD,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1D,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;YACxD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1D,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAC1D,OAAO,MAAM,CAAC;QAChB,CAAC;QAED,8BAA8B;QAC9B,IAAI,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,CAAC;YACtC,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,WAAW,EAAE,CAAC;YAElE,IAAI,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAAE,OAAO,MAAM,CAAC;YAC9C,IAAI,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAAE,OAAO,MAAM,CAAC;YAC9C,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC;gBAAE,OAAO,KAAK,CAAC;YAC5C,IAAI,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC;gBAAE,OAAO,KAAK,CAAC;QAC9C,CAAC;QAED,gDAAgD;QAChD,MAAM,UAAU,GAAG,MAAM,kBAAkB,EAAE,CAAC;QAC9C,IAAI,UAAU,EAAE,CAAC;YACf,OAAO,UAAU,CAAC;QACpB,CAAC;QAED,iBAAiB;QACjB,OAAO,KAAK,CAAC;IAEf,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,KAAK,CAAC,CAAC,kBAAkB;IAClC,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,kBAAkB;IAC/B,MAAM,eAAe,GAAgB,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IAEpE,KAAK,MAAM,EAAE,IAAI,eAAe,EAAE,CAAC;QACjC,IAAI,MAAM,qBAAqB,CAAC,EAAE,CAAC,EAAE,CAAC;YACpC,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,qBAAqB,CAAC,UAAkB;IACrD,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC;QACjE,QAAQ,CAAC,GAAG,OAAO,IAAI,UAAU,EAAE,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;QACzE,OAAO,IAAI,CAAC;IACd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,wBAAwB,CAAC,EAAa;IAC1D,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,QAAQ,CAAC,GAAG,EAAE,YAAY,EAAE;YACzC,QAAQ,EAAE,OAAO;YACjB,OAAO,EAAE,IAAI;YACb,KAAK,EAAE,MAAM;SACd,CAAC,CAAC;QACH,OAAO,MAAM,CAAC,IAAI,EAAE,CAAC;IACvB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,EAAa,EAAE,WAAmB,EAAE,SAAkB,KAAK;IAC3F,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC;IAEtC,QAAQ,EAAE,EAAE,CAAC;QACX,KAAK,KAAK;YACR,OAAO,eAAe,UAAU,IAAI,WAAW,EAAE,CAAC;QACpD,KAAK,MAAM;YACT,OAAO,QAAQ,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAI,WAAW,EAAE,CAAC;QAC5D,KAAK,MAAM;YACT,OAAO,MAAM,CAAC,CAAC,CAAC,mBAAmB,WAAW,EAAE,CAAC,CAAC,CAAC,YAAY,WAAW,EAAE,CAAC;QAC/E,KAAK,KAAK;YACR,OAAO,OAAO,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAI,WAAW,EAAE,CAAC;QAC3D,KAAK,MAAM;YACT,OAAO,gBAAgB,WAAW,EAAE,CAAC;QACvC;YACE,OAAO,eAAe,UAAU,IAAI,WAAW,EAAE,CAAC;IACtD,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,gBAAgB,CAAC,EAAa,EAAE,WAAmB,EAAE,SAAkB,KAAK;IAC1F,QAAQ,EAAE,EAAE,CAAC;QACX,KAAK,KAAK;YACR,OAAO,cAAc,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,WAAW,EAAE,CAAC;QAC3D,KAAK,MAAM;YACT,OAAO,QAAQ,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,IAAI,WAAW,EAAE,CAAC;QAClE,KAAK,MAAM;YACT,OAAO,MAAM,CAAC,CAAC,CAAC,uBAAuB,WAAW,EAAE,CAAC,CAAC,CAAC,gBAAgB,WAAW,EAAE,CAAC;QACvF,KAAK,KAAK;YACR,OAAO,cAAc,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,WAAW,EAAE,CAAC;QAC3D,KAAK,MAAM;YACT,OAAO,wBAAwB,WAAW,EAAE,CAAC;QAC/C;YACE,OAAO,cAAc,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,WAAW,EAAE,CAAC;IAC7D,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,mBAAmB,CAAC,EAAa,EAAE,WAAmB,EAAE,SAAkB,KAAK;IAC7F,QAAQ,EAAE,EAAE,CAAC;QACX,KAAK,KAAK;YACR,OAAO,iBAAiB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,WAAW,EAAE,CAAC;QAC9D,KAAK,MAAM;YACT,OAAO,QAAQ,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,QAAQ,IAAI,WAAW,EAAE,CAAC;QAClE,KAAK,MAAM;YACT,OAAO,MAAM,CAAC,CAAC,CAAC,sBAAsB,WAAW,EAAE,CAAC,CAAC,CAAC,eAAe,WAAW,EAAE,CAAC;QACrF,KAAK,KAAK;YACR,OAAO,cAAc,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,WAAW,EAAE,CAAC;QAC3D,KAAK,MAAM;YACT,OAAO,kBAAkB,WAAW,EAAE,CAAC;QACzC;YACE,OAAO,iBAAiB,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,WAAW,EAAE,CAAC;IAChE,CAAC;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,mBAAmB,CAAC,cAAsB,OAAO,CAAC,GAAG,EAAE;IACrE,MAAM,SAAS,GAAG;QAChB,EAAE,IAAI,EAAE,gBAAgB,EAAE,EAAE,EAAE,MAAmB,EAAE;QACnD,EAAE,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,KAAkB,EAAE;QAC7C,EAAE,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,MAAmB,EAAE;QAC9C,EAAE,IAAI,EAAE,mBAAmB,EAAE,EAAE,EAAE,KAAkB,EAAE;QACrD,EAAE,IAAI,EAAE,WAAW,EAAE,EAAE,EAAE,MAAmB,EAAE;KAC/C,CAAC;IAEF,KAAK,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,IAAI,SAAS,EAAE,CAAC;QACrC,IAAI,UAAU,CAAC,GAAG,WAAW,IAAI,IAAI,EAAE,CAAC,EAAE,CAAC;YACzC,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,qBAAqB,CAAC,EAAc;IAOxD,MAAM,UAAU,GAAG,EAAE,IAAI,MAAM,qBAAqB,EAAE,IAAI,KAAK,CAAC;IAChE,MAAM,OAAO,GAAG,MAAM,wBAAwB,CAAC,UAAU,CAAC,CAAC;IAC3D,MAAM,SAAS,GAAG,MAAM,qBAAqB,CAAC,UAAU,CAAC,CAAC;IAE1D,OAAO;QACL,IAAI,EAAE,UAAU;QAChB,OAAO;QACP,SAAS;QACT,cAAc,EAAE,iBAAiB,CAAC,UAAU,EAAE,iBAAiB,EAAE,IAAI,CAAC;QACtE,aAAa,EAAE,gBAAgB,CAAC,UAAU,EAAE,iBAAiB,EAAE,IAAI,CAAC;KACrE,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,qBAAqB;IAKzC,MAAM,eAAe,GAAgB,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IAC5E,MAAM,OAAO,GAAG,EAAE,CAAC;IAEnB,KAAK,MAAM,EAAE,IAAI,eAAe,EAAE,CAAC;QACjC,MAAM,OAAO,GAAG,MAAM,wBAAwB,CAAC,EAAE,CAAC,CAAC;QACnD,MAAM,SAAS,GAAG,MAAM,qBAAqB,CAAC,EAAE,CAAC,CAAC;QAElD,OAAO,CAAC,IAAI,CAAC;YACX,IAAI,EAAE,EAAE;YACR,OAAO;YACP,SAAS;SACV,CAAC,CAAC;IACL,CAAC;IAED,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB;IAK3C,MAAM,MAAM,GAAG,MAAM,qBAAqB,EAAE,CAAC;IAC7C,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;IAEvD,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC9B,OAAO;YACL,WAAW,EAAE,KAAK;YAClB,MAAM,EAAE,kDAAkD;YAC1D,YAAY,EAAE,EAAE;SACjB,CAAC;IACJ,CAAC;IAED,4CAA4C;IAC5C,MAAM,eAAe,GAAgB,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,CAAC,CAAC;IAEpE,KAAK,MAAM,SAAS,IAAI,eAAe,EAAE,CAAC;QACxC,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,SAAS,CAAC,CAAC;QAC7D,IAAI,KAAK,EAAE,CAAC;YACV,MAAM,YAAY,GAAG,YAAY;iBAC9B,MAAM,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,KAAK,SAAS,CAAC;iBACnC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;YAEtB,MAAM,OAAO,GAAG;gBACd,IAAI,EAAE,8DAA8D;gBACpE,GAAG,EAAE,uDAAuD;gBAC5D,IAAI,EAAE,kDAAkD;gBACxD,GAAG,EAAE,mDAAmD;aACzD,CAAC;YAEF,OAAO;gBACL,WAAW,EAAE,SAAS;gBACtB,MAAM,EAAE,OAAO,CAAC,SAAS,CAAC;gBAC1B,YAAY;aACb,CAAC;QACJ,CAAC;IACH,CAAC;IAED,8BAA8B;IAC9B,MAAM,QAAQ,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;IACjC,OAAO;QACL,WAAW,EAAE,QAAQ,CAAC,IAAI;QAC1B,MAAM,EAAE,iCAAiC;QACzC,YAAY,EAAE,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC;KACvD,CAAC;AACJ,CAAC"}