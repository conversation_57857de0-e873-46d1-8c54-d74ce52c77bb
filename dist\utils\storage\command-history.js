/**
 * Command History Management System
 *
 * Provides persistent command history with security features
 * Includes sensitive data filtering and configurable storage
 */
import { writeFileSync, readFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';
import { homedir } from 'os';
// Default configuration
const DEFAULT_CONFIG = {
    maxSize: 1000,
    saveHistory: true,
    sensitivePatterns: [
        'api[_-]?key',
        'password',
        'secret',
        'token',
        'auth',
        'credential',
        'private[_-]?key'
    ]
};
// History file path
const HISTORY_DIR = join(homedir(), '.kritrima-ai');
const HISTORY_FILE = join(HISTORY_DIR, 'command-history.json');
// In-memory history cache
let historyCache = null;
let configCache = null;
/**
 * Load history configuration
 */
function loadConfig() {
    if (configCache) {
        return configCache;
    }
    // For now, use default config
    // TODO: Load from user configuration
    configCache = { ...DEFAULT_CONFIG };
    return configCache;
}
/**
 * Load command history from disk
 */
function loadHistory() {
    if (historyCache) {
        return historyCache;
    }
    try {
        if (!existsSync(HISTORY_FILE)) {
            historyCache = [];
            return historyCache;
        }
        const content = readFileSync(HISTORY_FILE, 'utf-8');
        const data = JSON.parse(content);
        // Validate history entries
        if (Array.isArray(data)) {
            historyCache = data.filter(entry => entry &&
                typeof entry.command === 'string' &&
                typeof entry.timestamp === 'number');
        }
        else {
            historyCache = [];
        }
        return historyCache;
    }
    catch (error) {
        console.warn('Warning: Could not load command history:', error);
        historyCache = [];
        return historyCache;
    }
}
/**
 * Save command history to disk
 */
function saveHistory() {
    try {
        const config = loadConfig();
        if (!config.saveHistory) {
            return;
        }
        // Ensure directory exists
        if (!existsSync(HISTORY_DIR)) {
            mkdirSync(HISTORY_DIR, { recursive: true });
        }
        const history = loadHistory();
        writeFileSync(HISTORY_FILE, JSON.stringify(history, null, 2));
    }
    catch (error) {
        console.warn('Warning: Could not save command history:', error);
    }
}
/**
 * Check if command contains sensitive data
 */
function containsSensitiveData(command) {
    const config = loadConfig();
    const lowerCommand = command.toLowerCase();
    for (const pattern of config.sensitivePatterns) {
        const regex = new RegExp(pattern, 'i');
        if (regex.test(lowerCommand)) {
            return true;
        }
    }
    // Check for common sensitive patterns
    const sensitivePatterns = [
        /--?api[_-]?key\s*[=:]\s*\S+/i,
        /--?password\s*[=:]\s*\S+/i,
        /--?token\s*[=:]\s*\S+/i,
        /--?secret\s*[=:]\s*\S+/i,
        /export\s+\w*(?:key|password|token|secret)\w*\s*=/i,
        /set\s+\w*(?:key|password|token|secret)\w*\s*=/i
    ];
    for (const pattern of sensitivePatterns) {
        if (pattern.test(command)) {
            return true;
        }
    }
    return false;
}
/**
 * Add command to history
 */
export function addToHistory(command, success) {
    const config = loadConfig();
    if (!config.saveHistory) {
        return;
    }
    // Skip empty commands
    if (!command.trim()) {
        return;
    }
    // Skip sensitive commands
    if (containsSensitiveData(command)) {
        return;
    }
    const history = loadHistory();
    // Skip duplicate consecutive commands
    if (history.length > 0 && history[history.length - 1].command === command) {
        return;
    }
    // Add new entry
    const entry = {
        command: command.trim(),
        timestamp: Date.now(),
        success
    };
    history.push(entry);
    // Maintain size limit
    if (history.length > config.maxSize) {
        history.splice(0, history.length - config.maxSize);
    }
    // Save to disk
    saveHistory();
}
/**
 * Get command history
 */
export function getHistory() {
    return [...loadHistory()];
}
/**
 * Search command history
 */
export function searchHistory(query) {
    const history = loadHistory();
    const lowerQuery = query.toLowerCase();
    return history.filter(entry => entry.command.toLowerCase().includes(lowerQuery));
}
/**
 * Get recent commands
 */
export function getRecentCommands(count = 10) {
    const history = loadHistory();
    return history.slice(-count).reverse();
}
/**
 * Get command at index (for navigation)
 */
export function getCommandAtIndex(index) {
    const history = loadHistory();
    if (index < 0 || index >= history.length) {
        return null;
    }
    return history[index].command;
}
/**
 * Get history size
 */
export function getHistorySize() {
    return loadHistory().length;
}
/**
 * Clear command history
 */
export function clearHistory() {
    historyCache = [];
    saveHistory();
}
/**
 * Get history statistics
 */
export function getHistoryStats() {
    const history = loadHistory();
    if (history.length === 0) {
        return {
            totalCommands: 0,
            uniqueCommands: 0,
            successfulCommands: 0,
            failedCommands: 0
        };
    }
    const uniqueCommands = new Set(history.map(entry => entry.command)).size;
    const successfulCommands = history.filter(entry => entry.success === true).length;
    const failedCommands = history.filter(entry => entry.success === false).length;
    const timestamps = history.map(entry => entry.timestamp);
    const oldestTimestamp = Math.min(...timestamps);
    const newestTimestamp = Math.max(...timestamps);
    return {
        totalCommands: history.length,
        uniqueCommands,
        successfulCommands,
        failedCommands,
        oldestCommand: new Date(oldestTimestamp),
        newestCommand: new Date(newestTimestamp)
    };
}
/**
 * Export history to file
 */
export function exportHistory(outputPath) {
    try {
        const history = loadHistory();
        writeFileSync(outputPath, JSON.stringify(history, null, 2));
        return true;
    }
    catch (error) {
        console.warn('Warning: Could not export history:', error);
        return false;
    }
}
/**
 * Import history from file
 */
export function importHistory(inputPath, merge = false) {
    try {
        if (!existsSync(inputPath)) {
            return false;
        }
        const content = readFileSync(inputPath, 'utf-8');
        const importedHistory = JSON.parse(content);
        // Validate imported data
        if (!Array.isArray(importedHistory)) {
            return false;
        }
        const validEntries = importedHistory.filter(entry => entry &&
            typeof entry.command === 'string' &&
            typeof entry.timestamp === 'number');
        if (merge) {
            // Merge with existing history
            const existingHistory = loadHistory();
            const combinedHistory = [...existingHistory, ...validEntries];
            // Remove duplicates and sort by timestamp
            const uniqueHistory = Array.from(new Map(combinedHistory.map(entry => [entry.command + entry.timestamp, entry])).values()).sort((a, b) => a.timestamp - b.timestamp);
            historyCache = uniqueHistory;
        }
        else {
            // Replace existing history
            historyCache = validEntries.sort((a, b) => a.timestamp - b.timestamp);
        }
        saveHistory();
        return true;
    }
    catch (error) {
        console.warn('Warning: Could not import history:', error);
        return false;
    }
}
/**
 * Update history configuration
 */
export function updateConfig(newConfig) {
    const config = loadConfig();
    configCache = { ...config, ...newConfig };
    // If max size changed, trim history
    if (newConfig.maxSize !== undefined) {
        const history = loadHistory();
        if (history.length > newConfig.maxSize) {
            history.splice(0, history.length - newConfig.maxSize);
            saveHistory();
        }
    }
}
/**
 * Get navigation history for arrow key navigation
 */
export class HistoryNavigator {
    currentIndex = -1;
    history;
    constructor() {
        this.history = loadHistory();
    }
    /**
     * Get previous command
     */
    getPrevious() {
        if (this.history.length === 0) {
            return null;
        }
        if (this.currentIndex === -1) {
            this.currentIndex = this.history.length - 1;
        }
        else if (this.currentIndex > 0) {
            this.currentIndex--;
        }
        return this.history[this.currentIndex]?.command || null;
    }
    /**
     * Get next command
     */
    getNext() {
        if (this.history.length === 0 || this.currentIndex === -1) {
            return null;
        }
        if (this.currentIndex < this.history.length - 1) {
            this.currentIndex++;
            return this.history[this.currentIndex].command;
        }
        else {
            this.currentIndex = -1;
            return '';
        }
    }
    /**
     * Reset navigation
     */
    reset() {
        this.currentIndex = -1;
    }
    /**
     * Refresh history
     */
    refresh() {
        this.history = loadHistory();
        this.currentIndex = -1;
    }
}
//# sourceMappingURL=command-history.js.map