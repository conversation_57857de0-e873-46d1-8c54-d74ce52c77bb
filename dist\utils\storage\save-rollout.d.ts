/**
 * Session Rollout System
 *
 * Handles automatic session persistence with best-effort async saving
 * Provides session recovery and management capabilities
 */
import type { ResponseItem, SessionData, SessionMetadata } from '../../types/index.js';
/**
 * Save session rollout (best-effort async)
 */
export declare function saveRollout(sessionId: string, items: ResponseItem[], config?: any): void;
/**
 * Save session rollout asynchronously
 */
export declare function saveRolloutAsync(sessionId: string, items: ResponseItem[], config?: any): Promise<void>;
/**
 * Load session by ID
 */
export declare function loadSession(sessionId: string): SessionData | null;
/**
 * List all available sessions
 */
export declare function listSessions(): SessionMetadata[];
/**
 * Delete session by ID
 */
export declare function deleteSession(sessionId: string): boolean;
/**
 * Get session statistics
 */
export declare function getSessionStats(): {
    totalSessions: number;
    totalSize: number;
    oldestSession?: Date;
    newestSession?: Date;
};
/**
 * Export session to file
 */
export declare function exportSession(sessionId: string, outputPath: string): boolean;
/**
 * Import session from file
 */
export declare function importSession(inputPath: string): string | null;
/**
 * Format file size for display
 */
export declare function formatFileSize(bytes: number): string;
//# sourceMappingURL=save-rollout.d.ts.map