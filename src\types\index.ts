/**
 * Core Types and Interfaces
 * 
 * Defines the fundamental types used throughout the Kritrima AI CLI system
 */

import type { OpenAI } from 'openai';

// ============================================================================
// Provider & Model Types
// ============================================================================

export interface ProviderConfig {
  name: string;
  baseURL: string;
  envKey: string;
}

export type ProviderName = 
  | 'openai' 
  | 'azure' 
  | 'gemini' 
  | 'ollama' 
  | 'mistral' 
  | 'deepseek' 
  | 'xai' 
  | 'groq' 
  | 'arceeai' 
  | 'openrouter';

export interface ModelInfo {
  id: string;
  name: string;
  contextLength: number;
  supportsImages: boolean;
  supportsTools: boolean;
  provider: ProviderName;
}

// ============================================================================
// Configuration Types
// ============================================================================

export type ApprovalPolicy = 'suggest' | 'auto-edit' | 'full-auto';

export interface AppConfig {
  model: string;
  provider: ProviderName;
  approvalMode: ApprovalPolicy;
  providers?: Record<string, ProviderConfig>;
  safeCommands?: string[];
  dangerousCommands?: string[];
  maxTokens?: number;
  temperature?: number;
  timeout?: number;
  enableNotifications?: boolean;
  enableLogging?: boolean;
  projectDocPath?: string;
  additionalWritableRoots?: string[];
}

// ============================================================================
// Response & Message Types
// ============================================================================

export interface ResponseContentInput {
  type: 'input_text' | 'input_image';
  text?: string;
  image?: {
    url: string;
    detail?: 'low' | 'high' | 'auto';
  };
}

export interface ResponseInputItem {
  role: 'user' | 'assistant' | 'system';
  content: ResponseContentInput[];
  type: 'message';
  timestamp?: number;
}

export interface ResponseOutputItem {
  role: 'assistant';
  content: string;
  type: 'output';
  timestamp?: number;
  metadata?: {
    model: string;
    provider: string;
    tokens?: number;
    thinkingTime?: number;
  };
}

export interface ResponseFunctionToolCall {
  id: string;
  name: string;
  arguments: string;
  type: 'function_call';
  timestamp?: number;
}

export interface ResponseToolResult {
  id: string;
  result: string;
  success: boolean;
  type: 'tool_result';
  timestamp?: number;
  metadata?: {
    command?: string[];
    workdir?: string;
    exitCode?: number;
    duration?: number;
  };
}

export type ResponseItem = 
  | ResponseInputItem 
  | ResponseOutputItem 
  | ResponseFunctionToolCall 
  | ResponseToolResult;

// ============================================================================
// Tool & Function Types
// ============================================================================

export interface FunctionTool {
  type: 'function';
  function: {
    name: string;
    description: string;
    parameters: {
      type: 'object';
      properties: Record<string, any>;
      required: string[];
    };
  };
}

export interface ExecInput {
  command: string[];
  workdir?: string;
  timeout?: number;
}

export interface ExecResult {
  success: boolean;
  output: string;
  error?: string;
  exitCode: number;
  duration: number;
  command: string[];
  workdir: string;
}

// ============================================================================
// UI & Event Types
// ============================================================================

export type OverlayModeType = 
  | 'none' 
  | 'history' 
  | 'sessions' 
  | 'model' 
  | 'approval' 
  | 'help' 
  | 'diff';

export interface ConfirmationResult {
  decision: 'yes' | 'no_continue' | 'no_exit' | 'always' | 'explain';
  customMessage?: string;
}

export interface HistoryEntry {
  command: string;
  timestamp: number;
  success?: boolean;
}

// ============================================================================
// Session & Storage Types
// ============================================================================

export interface SessionMetadata {
  id: string;
  timestamp: number;
  model: string;
  provider: string;
  itemCount: number;
  lastActivity: number;
}

export interface SessionData {
  metadata: SessionMetadata;
  items: ResponseItem[];
  config: Partial<AppConfig>;
}

// ============================================================================
// Error Types
// ============================================================================

export class KritrimaError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'KritrimaError';
  }
}

export class NetworkError extends KritrimaError {
  constructor(message: string, details?: any) {
    super(message, 'NETWORK_ERROR', details);
  }
}

export class ConfigError extends KritrimaError {
  constructor(message: string, details?: any) {
    super(message, 'CONFIG_ERROR', details);
  }
}

export class SecurityError extends KritrimaError {
  constructor(message: string, details?: any) {
    super(message, 'SECURITY_ERROR', details);
  }
}
